import os

from flask import current_app as app, request
from flask_socketio import Namespace, emit

from director.db import load_db
from director.handler import ChatHandler


class ChatNamespace(Namespace):
    def on_connect(self):
        """Client connected."""
        app.logger.info(f"Client connected: {request.sid} to /chat namespace")
        # You can emit a welcome message or connection confirmation here if needed
        # emit("connection_ack", {"message": "Successfully connected to chat"}, room=request.sid)

    def on_disconnect(self):
        """Client disconnected."""
        app.logger.info(f"Client disconnected: {request.sid} from /chat namespace")

    def on_send_message(self, data):
        """Handle incoming chat messages from a client."""
        session_id = request.sid  # Or get from data if you manage sessions differently
        app.logger.info(f"Received message from {session_id}: {data}")

        try:
            # Ensure data contains necessary fields, e.g., 'message', 'agent_id', 'session_id'
            user_message = data.get("message")
            # agent_id = data.get("agent_id") # If applicable
            # client_session_id = data.get("session_id") # If client manages session IDs

            if not user_message:
                emit("error", {"message": "No message content provided"}, room=session_id)
                return

            # Initialize ChatHandler - ensure DB_TYPE is available in app.config
            db_type = os.getenv("SERVER_DB_TYPE", app.config.get("DB_TYPE", "sqlite"))
            chat_handler = ChatHandler(db=load_db(db_type))

            # This is a placeholder for how you might interact with ChatHandler
            # response = chat_handler.handle_chat_message(client_session_id, user_message, agent_id)
            # For now, let's just echo or send a placeholder response
            response_message = f"Agent acknowledges: '{user_message}' (handler logic pending)"
            emit("agent_response", {"message": response_message, "session_id": session_id}, room=session_id)
            app.logger.info(f"Sent response to {session_id}: {response_message}")

        except Exception as e:
            app.logger.error(f"Error processing message from {session_id}: {e}", exc_info=True)
            emit("error", {"message": f"An error occurred: {str(e)}"}, room=session_id)

    # Add other event handlers as needed, e.g., for joining rooms, specific agent interactions, etc.
