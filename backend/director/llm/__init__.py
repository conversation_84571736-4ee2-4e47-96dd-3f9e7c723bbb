import os
import logging

from director.constants import LLMType

from director.llm.openai import OpenAI
from director.llm.anthropic import AnthropicAI
from director.llm.googleai import GoogleAI
from director.llm.videodb_proxy import VideoDBProxy

logger = logging.getLogger(__name__)


def get_default_llm():
    """Get default LLM"""

    default_llm_type = os.getenv("DEFAULT_LLM")
    openai_api_key = os.getenv("OPENAI_API_KEY")
    anthropic_api_key = os.getenv("ANTHROPIC_API_KEY")
    googleai_api_key = os.getenv("GOOGLEAI_API_KEY")
    # videodb_api_key = os.getenv("VIDEO_DB_API_KEY") # VideoDBProxyConfig loads this directly

    # Prioritize DEFAULT_LLM if the corresponding API key is also set
    if default_llm_type == LLMType.OPENAI and openai_api_key:
        logger.info("Using OpenAI LLM based on DEFAULT_LLM and API key.")
        return OpenAI()
    if default_llm_type == LLMType.ANTHROPIC and anthropic_api_key:
        logger.info("Using Anthropic LLM based on DEFAULT_LLM and API key.")
        return AnthropicAI()
    if default_llm_type == LLMType.GOOGLEAI and googleai_api_key:
        logger.info("Using GoogleAI LLM based on DEFAULT_LLM and API key.")
        return GoogleAI()
    if default_llm_type == LLMType.VIDEODB_PROXY: # VideoDBProxy checks its own key
        logger.info("Using VideoDBProxy LLM based on DEFAULT_LLM.")
        return VideoDBProxy()

    # Fallback to the first available LLM if DEFAULT_LLM is not set or its key is missing
    if openai_api_key:
        logger.info("Using OpenAI LLM based on available API key (fallback).")
        return OpenAI()
    if anthropic_api_key:
        logger.info("Using Anthropic LLM based on available API key (fallback).")
        return AnthropicAI()
    if googleai_api_key:
        logger.info("Using GoogleAI LLM based on available API key (fallback).")
        return GoogleAI()
    
    # Final fallback to VideoDBProxy (it will raise an error if its API key is missing)
    logger.warning("No specific LLM configured or keys found, falling back to VideoDBProxy.")
    return VideoDBProxy()
