## AnthropicAI

AnthropicAI extends the base LLM and implements the Anthropic API.

### AnthropicAI Config

AnthropicAI Config is the configuration object for AnthropicAI. It is used to configure AnthropicAI and is passed to AnthropicAI when it is created.

::: director.llm.anthropic.AnthropicAIConfig

### AnthropicAI Interface

AnthropicAI is the LLM used by the agents and tools. It is used to generate responses to messages.

::: director.llm.anthropic.AnthropicAI
