## LLM Interface

Base LLM is the base class for all LLMs. It provides a common interface for all LLMs to follow.

### Base LLM Config

Base LLM Config is the configuration object for an LLM. It is used to configure the LLM and is passed to the LLM when it is created.

::: director.llm.base.BaseLLMConfig


### Base LLM

Base LLM is the base class for all LLMs. It provides a common interface for all LLMs to follow.

::: director.llm.base.BaseLLM

### LLM Response

LLM Response is the response object for an LLM. It is returned by the LLM after processing an input message.

::: director.llm.base.LLMResponse
