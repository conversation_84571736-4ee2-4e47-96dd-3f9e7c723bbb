{"root": ["./src/main.ts", "./src/vite-env.d.ts", "./src/app.vue", "./src/components/atoms/avatar.vue", "./src/components/atoms/button.vue", "./src/components/atoms/iconbutton.vue", "./src/components/collection/allvideos.vue", "./src/components/collection/allvideosloader.vue", "./src/components/collection/collectionprofile.vue", "./src/components/collection/collectionprofileloading.vue", "./src/components/collection/searchfield.vue", "./src/components/collection/searchsuggestions.vue", "./src/components/icons/commentline.vue", "./src/components/icons/linkicon.vue", "./src/components/icons/search.vue", "./src/components/icons/pause.vue", "./src/components/icons/play.vue", "./src/components/icons/time.vue", "./src/components/video/aidropdown.vue", "./src/components/video/menubutton.vue", "./src/components/video/videocard.vue", "./src/components/video/videocardloader.vue", "./src/components/video/videodetails.vue", "./src/components/video/videomenu.vue", "./src/views/collection.vue", "./src/views/video.vue"], "errors": true, "version": "5.6.2"}