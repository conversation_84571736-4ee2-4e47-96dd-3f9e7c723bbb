site_name: ""


repo_url: https://github.com/video-db/Director
repo_name: videodb-db/Director
edit_uri: edit/main/docs/
extra:
  generator: True

theme:
  name: material
  custom_dir: "docs/overrides"
  palette:
  - media: "(prefers-color-scheme)"
    toggle:
      icon: material/lightbulb-auto
      name: Switch to light mode
  - media: '(prefers-color-scheme: light)'
    scheme: default
    primary: custom
    accent: deep orange
    toggle:
      icon: material/lightbulb
      name: Switch to dark mode
  - media: '(prefers-color-scheme: dark)'
    scheme: slate
    primary: custom
    accent: deep orange
    toggle:
      icon: material/lightbulb-outline
      name: Switch to system preference
  features:
    - announce.dismiss
    - content.action.edit
    - content.action.view
    - content.code.annotate
    - content.code.copy
    - navigation.tabs
    - navigation.path
    - navigation.instant
    - navigation.top
    - navigation.tracking
    - navigation.footer
    - search.suggest
    - search.highlight
    - search.share
    - content.tabs.link
    - content.code.annotation
    - content.code.copy

  favicon: assets/favicon.png
  logo: assets/logo.png
  language: en

nav:
  - Get Started:
    - 'Intro': 'index.md'
    - 'Installation': 'get_started/install.md'
    - Quick Deployment:
      - 'Render': 'get_started/render.md'
      - 'Railway': 'get_started/railway.md'
    - 'Contributing': 'get_started/contributing.md'
  - Concepts:
    - 'Overview': 'concepts/overview.md'
  - Core:
    - 'Reasoning': 'core/reasoning.md'
    - 'Session': 'core/session.md'
  - Agents:
    - 'Interface': 'agents/interface.md'
  - Tools:
    - 'Interface': 'tools/interface.md'
  - LLM:
    - 'Interface': 'llm/interface.md'
    - Integrations:
      - 'OpenAI': 'llm/openai.md'
      - 'AnthropicAI': 'llm/anthropic.md'
      - 'GoogleAI': 'llm/googleai.md'
  - 'Database':
    - 'Interface': 'database/interface.md'
    - Integrations:
      - 'SQLite': 'database/sqlite.md'
  - 'Server':
    - 'Initialization': 'server/initialization.md'
    - 'API': 'server/api.md'
    - 'Socket.io': 'server/socketio.md'
    - Deployments:
      - 'Render': 'get_started/render.md'
      - 'Railway': 'get_started/railway.md'
  - 'Utilities':
    - 'Exceptions': 'utilities/exceptions.md'
  - 'Releases':
    - 'Notes': 'releases/notes.md'

plugins:
- search
- offline 
- autorefs
- mkdocstrings:
    handlers:
      python:
        options:
          extensions:
          - griffe_pydantic:
              schema: false
          show_root_heading: true
          show_if_no_docstring: false
          inherited_members: true
          docstring_style: sphinx
          members_order: source
          ignore_private: false
          separate_signature: true
          unwrap_annotated: false
          filters:
          - '!^model_config'
          merge_init_into_class: true
          docstring_section_style: table
          signature_crossrefs: true
          show_symbol_type_heading: true
          show_symbol_type_toc: true
- git-revision-date-localized:
    type: timeago
    locale: en
    enable_creation_date: true
    fallback_to_build_date: true

hooks:
 - docs/hooks/copyright.py

markdown_extensions:
  - abbr
  - admonition
  - attr_list
  - def_list
  - footnotes
  - md_in_html
  - toc:
      permalink: true
  - pymdownx.arithmatex:
      generic: true
  - pymdownx.betterem:
      smart_enable: all
  - pymdownx.caret
  - pymdownx.details
  - pymdownx.emoji:
      emoji_generator: !!python/name:material.extensions.emoji.to_svg
      emoji_index: !!python/name:material.extensions.emoji.twemoji
  - pymdownx.highlight:
      anchor_linenums: true
      line_spans: __span
      pygments_lang_class: true
  - pymdownx.inlinehilite
  - pymdownx.keys
  - pymdownx.magiclink:
      normalize_issue_symbols: true
      repo_url_shorthand: true
      user: squidfunk
      repo: mkdocs-material
  - pymdownx.mark
  - pymdownx.smartsymbols
  - pymdownx.snippets:
      auto_append:
        - includes/mkdocs.md
  - pymdownx.superfences:
      custom_fences:
        - name: mermaid
          class: mermaid
          format: !!python/name:pymdownx.superfences.fence_code_format
  - pymdownx.tabbed:
      alternate_style: true
      combine_header_slug: true
      slugify: !!python/object/apply:pymdownx.slugs.slugify
        kwds:
          case: lower
  - pymdownx.tasklist:
      custom_checkbox: true
  - pymdownx.tilde

extra:
  social:
    - icon: fontawesome/brands/github
      link: https://github.com/video-db
    - icon: fontawesome/brands/x-twitter
      link: https://x.com/videodb_io
    - icon: fontawesome/brands/discord
      link: https://discord.com/invite/py9P639jGz

extra_css:
  - stylesheets/extra.css
