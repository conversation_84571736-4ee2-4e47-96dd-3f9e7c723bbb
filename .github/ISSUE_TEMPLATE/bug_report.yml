name: Bug report
description: Create a report to help us improve
labels: ['bug']
body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to fill out this bug report!
  - type: checkboxes
    attributes:
      label: Confirm this is a new bug report
      options:
        - label: I've checked the current issues, and there's no record of this bug
          required: false
  - type: textarea
    attributes:
      label: Current Behavior
      description: >
        A clear and concise description of what the bug is.
      placeholder: >
        I intended to perform action X, but unexpectedly encountered outcome Y.
    validations:
      required: true
  - type: textarea
    attributes:
      label: Expected Behavior
      description: >
        A clear and concise description of what you expected to happen.
      placeholder: >
        I expected outcome Y to occur.
    validations:
      required: true
  - type: textarea
    attributes:
      label: Steps to Reproduce
      description: >
        Steps to reproduce the behavior:
      placeholder: |
        1. Fetch a '...'
        2. Update the '....'
        3. See error
    validations:
      required: true
  - type: textarea
    attributes:
      label: Relevant Logs and/or Screenshots
      description: >
        If applicable, add logs and/or screenshots to help explain your problem.
    validations:
      required: false
  - type: textarea
    attributes:
      label: Environment
      description: |
        Please complete the following information:
        eg:
        - OS: Ubuntu 20.04
        - Python: 3.9.1
        - VideoDB: 0.0.1
      value: |
        - OS: 
        - Python: 
        - VideoDB: 
    validations:
      required: false
  - type: textarea
    attributes:
      label: Additional Context
      description: >
        Add any other context about the problem here.
    validations:
      required: false
